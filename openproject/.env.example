##
# All environment variables defined here will only apply if you pass them
# to the OpenProject container in docker-compose.yml under x-op-app -> environment.
# For the examples here this is already the case.
#
# Please refer to our documentation to see all possible variables:
#   https://www.openproject.org/docs/installation-and-operations/configuration/environment/
#
TAG=15-slim
OPENPROJECT_HTTPS=false
OPENPROJECT_HOST__NAME=localhost
PORT=127.0.0.1:8080
OPENPROJECT_RAILS__RELATIVE__URL__ROOT=
IMAP_ENABLED=false
DATABASE_URL=***********************************************************************************
RAILS_MIN_THREADS=4
RAILS_MAX_THREADS=16
POSTGRES_PASSWORD=p4ssw0rd
PGDATA="/var/lib/postgresql/data"
OPDATA="/var/openproject/assets"
