	
	Os arquivos localizados na pasta autocal/configuration são essenciais para a criação da interface gráfica de usuário (GUI) com widgets GTK. Cada um desses arquivos contém classes especializadas destinadas a executar funções específicas. Essas classes definem e organizam os diversos elementos da interface gráfica, como botões, rótulos, caixas de combinação, botões de verificação, entre outros. Quando uma instância dessas classes é criada e adicionada à janela principal da aplicação, os elementos visuais correspondentes são exibidos na tela.
	Mais precisamente, os arquivos mencionados desempenham um papel crucial na composição da aba "Configurações" da GUI. Em outras palavras, no notebook<class Configuration>, os frames contidos nesses arquivos são instanciados e organizados para a apresentação na tela, contribuindo para a estrutura visual da seção de configurações da aplicação.


FRAME_SIGNAL_GENERATOR
	No arquivo frame_signal_generator, foi criada a classe Signal_generator com o objetivo de rastrear as interações do usuário relacionadas ao gerador de sinais. A função método set_signalgenerator(param1, param2) desta classe recebe dois parâmetros, que são ponteiros para as classes 'Frequency_sweep' e 'Frequency', respectivamente. Esses ponteiros proporcionam acesso às variáveis públicas que devem ser monitoradas, e que têm o potencial de alterar o estado do gerador de sinais.
	Este frame concentra toda a troca de informação com o gerador de sinais.
	
FRAME_PRE_AMPLIFIER
	No arquivo frame_pre_amplifier, foi definida a classe Signal_generator, que tem o propósito de monitorar as interações do usuario na tela relacionadas ao pré amplificador, enviando comandos ao equipamento quando necessário.
	Este frame concentra toda a troca de informação com o pré amplificador.

FRAME_FILTER,
	No arquivo frame_filter, foi definida a classe Filter, que tem o propósito de monitorar as interações do usuario na tela relacionadas ao filtro, enviando comandos ao equipamento quando necessário.
	Este frame concentra toda a troca de informação com o filtro.

FRAME_FILE_MANAGEMENT
	No arquivo frame_file_management, foi definida a classe File_management, que tem o propósito de monitorar as interações do usuario na tela relacionadas a leitura e gravação das configurações do programa em um arquivo.
	O tipo de arquivo e como será salvo , aindanão foi implementado.  

FRAME_CALIBRATION_CONDITIONS
	No arquivo frame_calibration_conditions, foi definida a classe Calibration_conditions, que permite informar o tempo da primeira reflexão, tempo de chegada dafrente de onda e a distancia entre transdutores, bem como a temperatura da água.
	Obs: Calculo não implementado.	
	
FRAME_FREQUENCY_SWEEP
	No arquivo frame_frequency_sweep, a classe Frequency_sweep foi definida com a finalidade de rastrear as interações do usuário relacionadas ao gerador de sinais. Esta classe informa à classe Signal_generator sobre as ações do usuário, que, por sua vez, envia comandos correspondentes ao equipamento.

FRAME_TRANSMISSION_RECEPTION
	No arquivo frame_transmission_reception, foi definida a classe Transmission_reception, que permite ao usuário informar, um identificador, como, o nome e o número de série do trandutor, bem como o número transmissões e recepções que serão realizadas.

FRAME_FREQUENCY
	No arquivo frame_frequency, foi definida a classe Frequency, que permite ao usuário informar, a frequencia inicial e final da transmissão a ser realizada, bem como, o "passo" entre cada tranmissão.
	 
FRAME_SAMPLING_WINDOW
	No arquivo frame_sampling_window, foi definida a classe Sampling_window, que após o ajuste do osciloscópio, informa na tela os valores da janela de amostragem, setada no equipamento.
	A leitura da janela de amostragem é feita com o auxílio das caixas de dialogo dialog_delay e dialog_window.

FRAME_AZIMUTH
	Não implementado.

DIALOG_DELAY
	No arquivo dialog_delay, foi definida a classe Dialog_delay, que permite a leitura dos cursores ajustados no osciloscópio e informar em tela seus respectivos valores, que melhor se ajustaram a leitura dos sinais transmitidos e recebidos.

DIALOG_WINDOW
	No arquivo dialog_window, foi definida a classe Dialog_window, que permite a leitura dos cursores e da janela de amostragem, ajustados no osciloscópio e informar em tela seus respectivos valores, que melhor se ajustaram a leitura dos sinais transmitidos e recebidos.
	
	
	
	
	
