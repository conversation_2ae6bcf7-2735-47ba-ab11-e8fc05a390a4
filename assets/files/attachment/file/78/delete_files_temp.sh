#!/bin/bash

#Diretório para atualização

# Descrição:
#	Script para realizar deletar arquivos na pasta Temp da compartilhada do servidor Oberon.
#	Deve ser inserido no crontab do root com 'sudo crontab -e' com a linha abaixo (sem o '#'), para realizar o backup todos os dias as 22h00:
#0 22 * * * /path-to-file/oberon_smb_backup.sh

diretorio='/media/sonar/20ef8ade-9fa4-45e0-9c11-5f2ac62d39b5/Shared_folder/Temp'
if [ -d "$diretorio" ]; then
	find "$diretorio" -mtime +7 -exec rm -r '{}' +
fi
