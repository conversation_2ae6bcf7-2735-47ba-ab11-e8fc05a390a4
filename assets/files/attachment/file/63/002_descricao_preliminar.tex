\documentclass[doctype=esp,secrecy=reservado,sector=13,minute,titleacronym=SONAT_MKIII,volume=1/1,docnumber=2]{ipqmdoc}

\usepackage{rotating} % Rotating table
\usepackage{array,booktabs}
\usepackage{makecell}
\usepackage{pdflscape}
\usepackage{tabularray}
\usepackage{tabularx}
\usepackage{acro}

\usepackage{xcolor, colortbl}

\definecolor{gray}{rgb}{0.85,0.85,0.85}

\projectname{Sonar Nacional Ativo}
\projectacronym{SONAT}
\title{Descrição Preliminar}

\writtenby{CT Marlon Jovenil de Souza\\Eng. <PERSON>\\1ºTen (EN) Caio <PERSON>\\1ºTen (EN) Vin<PERSON><PERSON>}{DD/MM/AAAA}
\revisedby{CT <PERSON> Men<PERSON>\\1ºTen (EN) <PERSON>}{DD/MM/AAAA}
\ratifiedby{CF (EN) <PERSON>}{DD/MM/AAAA}

\approvedby{<PERSON>}{Servidor Civil Nível Superior}{Superintendente de Pesquisas e Desenvolvimento}{DD de MÊS de AAAA}

\referencefile{../refs} % Caminho/nome do arquivo de bibliografia, sem a extensão .bib.
\input{../resource/acronimos.tex}
\summary{Este documento apresenta a descrição preliminar do sistema \acs{sonat} Mk III.}
\keywords{Descrição Preliminar, Manual, \acs{sonat} e \acs{gsas}.}

\begin{document}

\maketitle % Capa

\coversheet % Folha de rosto

\approval % Ato de Aprovação

\tableofcontents % Sumário

\listoffigures % Lista de figuras [Opcional]

\listoftables % Lista de tabelas [Opcional]

\printacronyms % Siglas e abreviaturas [Opcional]

\section{Objetivo}\label{subsec:objetivo}
O objetivo do projeto \ac{sonat} é o desenvolvimento de sistema de \ac{sonar} ativo para revitalização dos sistemas \ac{sonar} EDO 997 das \acp{fcn} e \ac{ccb}, instalado nas Fragatas \acp{f41}, \acp{f42}, \acp{f43} e \acp{f44}. 

O presente documento visa descrever, de forma sucinta, o \ac{sonat} versão Mk III, no que diz respeito a: componentes e funcionalidades em comparação com o \ac{sonat} versão Mk II e com o sistema \ac{sonar} EDO 997.

\subsection{Descrição geral do sistema}\label{subsec:escopo}
O sistema \ac{sonar} tem por objetivo detectar e acompanhar contatos submarinos por meio ativo ou passivo. O operador do sistema tem a capacidade de controlar seus modos de operação e monitorar seu estado de funcionamento. O sistema permite a operação nos modos ativo e passivo e disponibiliza funções que auxiliam o operador no processo de classificação dos contatos e acompanhamentos.

O sistema é composto por: um conjunto de transdutores que compõem o \ac{cha}, instalado no casco do navio e protegido por um domo; unidades de transmissão, recepção, condicionamento e processamento de sinais, como ilustrado na Figura \ref{fig:arquitetura_sonar_edo997}; e um console de operação que permite o acesso às funções de sistema e à seleção dos vários modos de transmissão.

\begin{figure}[!ht]
\centering
	\caption{Arquitetura Simplificada do SONAR EDO 997.}
	\includegraphics[width=0.75\linewidth]{../resource/pictures/sistema_edo997.png}

    Fonte: Referência \cite{manual_EDO610_vol1}.
\label{fig:arquitetura_sonar_edo997}
\end{figure}

No EDO 997, a Unidade 4 é composta pelos módulos 4A1 (painel superior de indicadores e disjuntores), 4A2 (gabinete de processamento e recepção), 4A3 (gabinete de controle de transmissão e conversão \ac{da}), 4A4 (painel de disjuntores e fontes) e 4A5 (gabinete de \ac{ups}). A disposição da Unidade 4 no gabinete é mostrada na Figura \ref{fig:unidades_edo997_4}.

\begin{figure}[!ht]
\centering
    \caption{Detalhamento das Unidades 1 e 4}
    \subfloat[Unidade de Recepção (Unidade 4)\label{fig:unidades_edo997_4}]{\includegraphics[height=7.5cm]{../resource/pictures/unidade4_edo997.png}} \quad
    \subfloat[Console do Operador (Unidade 1)\label{fig:unidades_edo997_1}]{\includegraphics[height=7.5cm]{../resource/pictures/unidade1_edo997.png}}

    Fonte: Referência \cite{manual_EDO610_vol4}.
\label{fig:unidades_edo997}
\end{figure}

% \begin{figure}[!ht]
% \centering
% 	\caption{Detalhamento da Unidade de Recepção e Processamento (Unidade 4). Fonte \cite{manual_EDO610_vol4}.}
% 	\includegraphics[width=.50\linewidth]{../resource/pictures/unidade4_edo997.png}
% \label{fig:unidade4_edo997}
% \end{figure}

Os transmissores (Unidades 6, 7 e 8) são os componentes que amplificam o sinal a ser transmitido pelo transdutor, os quais são gerados de acordo com o modo de operação selecionado no Console do Operador (Unidade 1). No EDO 997, a Unidade 1 é composta pelos módulos 1A1 (painel frontal), 1A2 (unidade de display superior), 1A3 (unidade de display inferior), 1A4 (painel de bancada) e 1A5 (conjunto de armazenamento e processamento), mostrados na Figura \ref{fig:unidades_edo997_1}.

% \begin{figure}[!ht]
% \centering
% 	\caption{Detalhamento do Console do Operador (Unidade 1). Fonte \cite{manual_EDO610_vol4}.}
% 	\includegraphics[width=.6\linewidth]{../resource/pictures/unidade1_edo997.png}
% \label{fig:unidade1_edo997}
% \end{figure}

O Condicionador de Sinais (Unidade 9) chaveia o transdutor entre os instantes de transmissão e recepção e realiza a pré-amplificação e filtragem analógica dos sinais recebidos pelo Transdutor. A interface entre Condicionador e Transdutor consiste de duas Caixas de Junção (Unidades 13 e 14), as quais dispõem de transformadores para casamento de impedância entre os circuitos dos \textit{staves} dos transdutores e a saída do Transmissor.

O \ac{cha} é um conjunto composto por 360 elementos transdutores hidroacústicos dispostos de forma cilíndrica em 36 \textit{staves} verticais, cada um deles com 10 elementos transdutores. O Treinador (Unidade 17) é o componente que permite ao operador injetar sinais simulados no receptor para fins de treinamento.

\subsection{Situação atual}
Os sistemas \ac{sonar} de casco das \ac{fcn} têm apresentado grandes restrições operacionais relacionadas às elevadas taxas de avarias devido ao tempo de uso. Além disso, as \acp{omps} têm se deparado com a escassez de sobressalentes, o que onera, dificulta e, por vezes, impossibilita o reparo devido à obsolescência dos componentes do sistema. 

Os componentes mais afetados são os \textit{octal-processors} e \acp{ad} situados nas Unidades 1 (Console do Operador) e 4 (Aquisição e Processamento de Sinais). Assim, o projeto \ac{sonat} visa revitalizar essas unidades, enquanto aproveita as demais unidades do sistema original, para compor um \ac{sonar} completo e funcional.

\section{Sistema SONAT}
\subsection{Unidade 1}
A Unidade 1 do \ac{sonat} é composta por um computador robustecido, um \textit{switch} de 16 portas e dois monitores \textit{touch screen} 24 polegadas. A Figura \ref{fig:comparativo_unidade1} apresenta uma imagem da versão original e da revitalizada pelo projeto.

\begin{figure}[!ht]
\centering
    \caption{Comparativo da Unidade 1 em relação ao sistema original}
    \subfloat[EDO 997\label{fig:comparativo_unidade1_edo}]{\includegraphics[height=7.5cm]{../resource/pictures/edo997_unit1.png}} \quad
    \subfloat[\ac{sonat} Mk III\label{fig:comparativo_unidade1_sonat}]{\includegraphics[height=7.5cm]{../resource/pictures/unidade1_sonat_mkiii.jpg}}
\label{fig:comparativo_unidade1}
\end{figure}

% \begin{figure}[!ht]
% \centering
% 	\caption{Componentes Internos da Unidade 1. TODO: trocar}
% 	\includegraphics[width=0.8\linewidth]{../resource/pictures/unidade1_computador_01.jpg}
% \label{fig:unidade1_computador_01}
% \end{figure}

% \begin{figure}[!ht]
% \centering
% 	\caption{Unidade 1 sem monitor superior. TODO: trocar}
% 	\includegraphics[width=0.6\linewidth]{../resource/pictures/unidade1_computador_02.jpg}
% \label{fig:unidade1_computador_02}
% \end{figure}

% \begin{figure}[!ht]
% \centering
% 	\caption{Unidade 1 no \acs{sonat} Mk III.}
% 	\includegraphics[width=0.6\linewidth]{../resource/pictures/unidade1_sonat_mkiii.png}
% \label{fig:unidade1_sonat_mkiii}
% \end{figure}

% A Figura \ref{fig:unidade1_edo_vs_sonat} apresenta uma imagem da versão original e da revitalizada pelo projeto.

% \begin{figure}[!ht]
% \centering
% 	\caption{Unidade 1: Versão original e versão revitalizada.}
% 	\includegraphics[width=0.6\linewidth]{../resource/pictures/unidade1_edo_vs_sonat.jpg}
% \label{fig:unidade1_edo_vs_sonat}
% \end{figure}

A Tabela \ref{tab:equips_substituidos_unidade1} apresenta os elementos da Unidade 1 que foram substituídos, ao passo que a Tabela \ref{tab:equips_removidos_unidade1} apresenta os elementos removidos da Unidade 1.

\begin{table}[!ht]
    \caption{Equipamentos Substituídos na Unidade 1}\label{tab:equips_substituidos_unidade1}
    \begin{tabularx}{\textwidth}{|>{\centering\arraybackslash}m{3.5cm} |>{\centering\arraybackslash}X |>{\centering\arraybackslash}m{3.5cm} |>{\centering\arraybackslash}m{3.25cm}|}
    \hline\rowcolor{lightgray}
    \multicolumn{2}{|c|}{\textbf{Equipamento}} & \multicolumn{2}{c|}{\textbf{Substituído por}} \\
    \hline\rowcolor{lightgray}
    EDO 997 & Descrição & \ac{sonat} Mk II & \ac{sonat} Mk III\\
    \hline
    1A3A1 e 1A2A1   & Dois Monitores CRT                & Dois monitores touch 24'' & - \\
    \hline
    1A2A2A1         & Um \textit{switch} ethernet 10/100         & Um \textit{switch} ethernet 10/100/1000 & - \\
    \hline
    1A5N1 e 1A5N2   & Dois \textit{assemblies} completos& Um Computador Convencional & Um Computador Robustecido  \\
    \hline
    \end{tabularx}
\end{table}

\begin{table}[!ht]
    \caption{Equipamentos Removidos da Unidade 1}\label{tab:equips_removidos_unidade1}
    \begin{tabularx}{\textwidth}{|>{\centering\arraybackslash}m{6cm} |>{\centering\arraybackslash}X |>{\centering\arraybackslash}m{6.5cm}|}
    \hline\rowcolor{lightgray}
    \textbf{Equipamento} & \textbf{Descrição} & \textbf{Observação} \\
    \hline
    1A2A2A2, 1A2A2A3, 1A5A22, 1A5A23, 1A5A30, e 1A5A31   & \textit{Transceivers}               & Não mais necessários com a nova interface Ethernet \\
    \hline
    \end{tabularx}
\end{table}

O computador possui três interfaces de rede: uma conectada diretamente ao \textit{switch} para comunicação com a rede interna do \ac{sonar}; e as demais conexões são responsáveis pela comunicação com o sistema de combate. %O switch possui as seguintes conexões: computador da Unidade 1; dois cabos para a Unidade 4; e dois cabos para a Unidade 1A5 (TODO: VRF), que permitem conexão de um laptop para teste. %24/10/2023: Fernando disse que é melhor comentar este texto.

\subsection{Unidade 4}
No \ac{sonat}, a Unidade 4 é composta por:
\begin{itemize}
    \item Computador robustecido de rack de 19 polegadas, contendo os conversores ac{ad} e \ac{da} e a placa optoacoplada;
    \item Gaveta de \textit{breakout} para conexões entre o sistema original e os conversores;
    \item Gaveta com fontes e disjuntores comerciais, visando facilitar a aquisição e manutenção; e
    \item \ac{ups} moderna.
\end{itemize}
% Conforme pode ser visto de baixo para cima na Figura \ref{fig:unidade4_rack_aberto}, a Unidade 4 do \ac{sonat} é composta por um computador robustecido, uma gaveta com breakouts, a unidade 4A4 original (painel de disjuntores e fontes), e uma \ac{ups} revitalizada. O computador robustecido contém os conversores \ac{ad} e \ac{da}, além da interface de rede para comunicação com a Unidade 1. A Figura \ref{fig:unidade4_edo_vs_sonat} apresenta uma imagem da versão original e da revitalizada pelo projeto.


% \begin{figure}[!ht]
%     \centering
%         \caption{Unidade 4 revitalizada TODO: Inserir Figura nova.}
%         \includegraphics[width=0.6\linewidth]{../resource/pictures/unidade4_rack_vazio.png}
%     \label{fig:unidade4_rack_aberto}
% \end{figure}

% \begin{figure}[!ht]
%     \centering
%         \caption{Unidade 4 original e revitalizada TODO: Inserir Figura nova p/ comparar com antiga.}
%         \includegraphics[width=0.6\linewidth]{../resource/pictures/unidade4_edo_vs_sonat.jpg}
%     \label{fig:unidade4_edo_vs_sonat}
% \end{figure}

A Tabela \ref{tab:equips_removidos_unidade4} apresenta os elementos da Unidade 4 que foram substituídos.

\begin{table}[!ht]
    \caption{Equipamentos Substituídos da Unidade 4}\label{tab:equips_removidos_unidade4}
    \begin{tabularx}{\textwidth}{|>{\centering\arraybackslash}X|>{\centering\arraybackslash}m{3.5cm} |>{\centering\arraybackslash}m{9cm}|}
    \hline\rowcolor{lightgray}
    \textbf{Equipamento} & \textbf{Descrição} & \textbf{Substituído por} \\
    \hline
    4A2 e 4A3   & Dois \textit{assemblies} completos & Computador robustecido com conversores \acs{ad} e \acs{da}. Interface de entradas e saídas optoacopladas. \\
    \hline
    4A4         & Gaveta de fontes e \textit{circuit breakers} & Gaveta de fontes modernizada\\
    \hline
    4A5         & \acs{ups}             & \acs{ups} Moderna\\
    \hline
    \end{tabularx}
\end{table}


\subsection{Funcionalidades}
A Tabela \ref{tblr:funcionalidades_implementadas} apresenta um comparativo entre o sistema original, o \ac{sonat} Mk II e o \ac{sonat} Mk III quanto às funcionalidades de cada sistema.

%%----- Big table
\def\tblrcontheadname{AAAA}
\def\tblrcontfootname{BBBB}
% \DefTblrTemplate{contfoot-text}{default}{Continued on next page}
% \SetTblrTemplate{contfoot-text}{default}
% \DefTblrTemplate{conthead-text}{default}{(Continued)}
% \SetTblrTemplate{conthead-text}{default}
\DefTblrTemplate{contfoot-text}{default}{Continua na próxima página}
\DefTblrTemplate{conthead-text}{default}{\textbf{(Continuação)}}
\DefTblrTemplate{caption-tag}{default}{\textbf{Tabela \thetable}}
\DefTblrTemplate{caption-sep}{default}{ -- }
% \DefTblrTemplate{caption}{default}{}
% \DefTblrTemplate{conthead}{default}{}
% \DefTblrTemplate{capcont}{default}{}

\begin{landscape}
\begin{longtblr}[
%theme = fancy,
caption = {\textbf{Funcionalidades implementadas por cada respectivo sistema}},
entry = {Funcionalidades implementadas por cada respectivo sistema},
label = {tblr:funcionalidades_implementadas},
note{$*$} = {Gráficos de Detecção de Energia em Cachoeira (tempo indexado na vertical, de cima para baixo).},
note{$\dag$} = {Gráfico de varredura em distância dividido em feixes. Em cada feixe, o tempo é indexado na horizontal, da direita para a esquerda},
note{$\ddagger$} = {Ponto focal (Centróide) do agrupamento de detecções.},
remark{Nota} = {``-'': Não possui a funcionalidade, ``-+'': Possui a funcionalidade em partes, ``+'': Possui a funcionalidade e ``++'': Estende a funcionalidade.},
]{
    colspec = {|Q[m,c,2.75cm]|Q[m,c,2.75cm]|Q[m,c,13.25cm]|Q[m,c,1.5cm]|Q[m,c,1.cm]|Q[m,c,1.cm]|},
    row{1} = {lightgray},
    width = 0.85\linewidth,
    rowhead = 1,
    rowfoot = 0
}
    \hline
    \textbf{Grupo} & \textbf{Funcionalidade} & \textbf{Descrição} & \textbf{EDO997} & \textbf{Mk II} & \textbf{Mk III} \\
    \hline
    %%
    \SetCell[r=4]{m} \makecell{Processamento\\Passivo} & Energia & Gráficos de energia em \emph{Waterfall}\TblrNote{$*$} para duas configurações de banda e dois tempos de integração simultâneos & + & ++ & ++\\
    %
    & Banda & Operação com frequências até 15 kHz (EDO 997 é limitado a 5 kHz) & -+ & + & +\\
    %
    & Análise & Acompanhamento e análise \ac{lofar} e \ac{demon} para áudio de até oito contatos simultâneos & - & + & +\\
    %
    & Áudio & Aúdio com seleção manual de banda desejada (filtro de áudio) e de contato/marcação a se ouvir. Processamento de UT (Telefonia Submarina). & + & ++ & ++\\
    \hline
    %%
    \SetCell[r=5]{m} \makecell{Processamento\\Ativo} & Detecção & Gráficos de detecção em \emph{BeamScan}\TblrNote{$\dag$} e em Polar para os ecos recebidos. Gráfico de \emph{clusters}\TblrNote{$\ddagger$} em \emph{BeamScan}\TblrNote{$\dag$} & + & + & ++\\
    %
    & Rastreamento & Realiza Criação e Acompanhamento, ambos de forma automática, de contatos. & + & -+ & ++\\
    %
    & Transmissão & Modos de Transmissão ODT, SRDT, ARDT, TRDT e MCC, conforme previstos no EDO997. Seleção de Sinais CW e/ou FM, de tempo de pulso e de frequência. & + & + & +\\
    %
    & Áudio & Permite ao operador ouvir, com deslocamento espectral, os ecos recebidos. & + & -+ & ++\\
    %
    & Parametrização & Apresentação tridimensional dos ecos recebidos em múltiplos pings. & + & - & -\\
    \hline
    %%
    \SetCell[r=1]{m} Comunicação & Combate & Envio e recebimento de contatos; recebimento de sinais de plataforma; envio de alerta de torpedo; e envio de estado e modo de operação do SONAR. Controle de interface \emph{syncro} e indicador \emph{ON TARGET} do sistema de lançamento de torpedo & + & -+ & -+\\
    %
    \SetCell[r=3]{m} Comunicação & UDSN & Recebimento de sinais de plataforma. & + & - & +\\
    %
    & Treinamento & Comunicação com Laptop para a realização de adestramentos internos. & + & - & +\\
    %
    & Previsão & Gráficos de excesso de sinal dado um perfil térmico do mar por profundidade. & + & - & -\\
    \hline
\end{longtblr}
\end{landscape}
%%---- End Big table


% \subsection{Descrição dos Manuais}\label{subsec:descricao_manuais}
% Os manuais do \ac{sonat} Mk III são divididos em: Descrição do Sistema, Instalação do
% Sistema, Operação do Sistema e Manutenção do Sistema.

% Este manual, intitulado "Descrição do Sistema", tem como objetivo apresentar um breve histórico 
% justificando as alterações realizadas no SONAR EDO997. Ademais, proporciona uma visão macro da 
% distribuição física e lógica tanto do SONAR EDO997 quanto do sistema \ac{sonat} Mk III, destacando 
% suas diferenças. Além disso, oferece uma visão geral das funcionalidades do software de operação 
% do SONAR, o Sistema de Detecção e Acompanhamento de Contatos para navios de Superfície (SDAC-SUP).

% O Manual de Instalação abrange o procedimento completo de instalação do \ac{sonat} Mk III, baseado nas 
% instalações realizadas nas Fragatas revitalizadas. Ele detalha os procedimentos para a remoção dos 
% equipamentos e componentes originais do EDO997 das Unidades 1 e 4, além de instruir sobre o posicionamento 
% e conexões dos equipamentos e componentes do \ac{sonat}.

% O Manual de Operação tem por objetivo apresentar detalhadamente o sistema \ac{sonat}-Mk III, 
% com foco especial em sua operação em conjunto com o SDAC-SUP. São abordadas as operações 
% nos modos de escuta passiva e ativa, destacando as funcionalidades específicas disponíveis 
% em cada modo de operação. Além disso, este manual oferece uma explanação completa do uso do 
% sistema, permitindo um entendimento profundo de suas capacidades e recursos.

% O Manual de Manutenção do Sistema abrange uma série de testes e procedimentos essenciais 
% para a manutenção adequada do sistema \ac{sonat}. Nele, estão detalhados os procedimentos de 
% diagnóstico e resolução de possíveis problemas que possam surgir durante a operação. 
% Com informações precisas e orientações claras, este manual é um recurso indispensável 
% para garantir o desempenho ideal e a confiabilidade do Sistema \ac{sonat}.

% \subsection{Breve Histórico}\label{subsec:historico}

% As FCN têm enfrentado consideráveis restrições operacionais nos sistemas SONAR de casco devido às 
% frequentes avarias causadas pelo tempo de uso. Além disso, as Organizações Militares
% Prestadoras de Serviço (OMPS) enfrentam desafios relacionados à escassez de sobressalentes, 
% o que resulta em dificuldades e, por vezes, impossibilidade de realizar os reparos devido à 
% obsolescência dos componentes do sistema.

% O projeto \ac{sonat} tem como objetivo revitalizar o SONAR EDO 997 instalado nas FCN e CCB, 
% modernizando as Unidades 1 (Console do Operador) e 4 (Aquisição e Processamento de Sinais), 
% que apresentam as maiores falhas em seus componentes. O propósito é restabelecer sua 
% operacionalidade, considerando a capacidade das Organizações Militar Prestadoras de Serviço 
% (OMPS), especialmente o Centro de Manutenção de Sistemas (CMS), de realizar a manutenção 
% das Unidades não revitalizadas pelo projeto.

% Após os levantamentos realizados na embarcação F42 e como parte do desenvolvimento da 
% versão Mk II do sistema \ac{sonat}, a versão Mk III revitalizou os módulos 4A2, 4A3, 4A4 e 4A5 
% da Unidade 4, juntamente com os módulos 1A1, 1A2, 1A3, 1A4, 1A5 e 1A3CB1 da Unidade 1. 
% Além disso, o software de aquisição, geração, controle, processamento, exibição e interação 
% com sistemas adjacentes também foi atualizado. Os gabinetes e cabeamentos das Unidades 
% revitalizadas foram mantidos para garantir a integridade da estrutura.

% \section{Sistema}\label{sec:sistema}

% \subsection{Sistema EDO 997}\label{subsec:sistema_edo997}

% O Sistema SONAR tem como objetivo detectar e acompanhar contatos submarinos através de 
% operação ativa ou passiva. O operador do sistema possui a capacidade de controlar os 
% modos de operação e monitorar seu estado de funcionamento. O sistema oferece a 
% funcionalidade de operação nos modos ativo e passivo, além de disponibilizar funções 
% que auxiliam o operador no processo de classificação e acompanhamento dos contatos.

% O sistema é composto por um conjunto de transdutores instalados no casco do navio, 
% protegidos por um domo. Além disso, inclui unidades de transmissão, recepção, 
% condicionamento e processamento de sinais, como ilustrado na Figura \ref{fig:arquitetura_sonar_edo997}. 
% Um console de operação permite o acesso às funções do sistema e a seleção dos vários 
% modos de transmissão.

% \begin{figure}[!h]
%     \centering{
%     \includegraphics[width=.95\linewidth]{/home/<USER>/latex/Sonat_Mk III/resource/pictures/sistema_edo997.png}
%     }
%     \caption{Arquitetura Simplificada do Sistema SONAR.}
%     \caption*{xxx}
%     \label{fig:arquitetura_sonar_edo997}
% \end{figure}

% A Figura \ref{fig:arquitetura_sonar_edo997} apresenta as Unidades componentes do sistema e 
% suas respectivas interfaces. A Unidade 4, conhecida como Receptor/Processador, desempenha 
% funções cruciais no processamento de sinais do sistema, tanto nos modos ativo quanto passivo, 
% incluindo a conversão Analógico-Digital (A/D) do sinal, a conformação de feixe passiva e 
% ativa e o controle da transmissão.

% No sistema EDO 997, a Unidade 4 é composta pelos seguintes módulos:

% \begin{itemize}
%     \item Módulo 4A1: Painel superior de indicadores e disjuntores;
%     \item Módulo 4A2: Gabinete de processamento e recepção;
%     \item Módulo 4A3: Gabinete de controle de transmissão e conversão D/A;
%     \item Módulo 4A4: Painel de disjuntores e fontes de alimentação; e
%     \item Módulo 4A5: Gabinete de UPS (Sistema de Alimentação Ininterrupta).
% \end{itemize}
    
% A disposição física dos módulos da Unidade 4 no gabinete é ilustrada na Figura 
% \ref{fig:unidade4_edo997}.

% \begin{figure}[!h]
%     \centering{
%     \includegraphics[width=.50\linewidth]{/home/<USER>/latex/Sonat_Mk III/resource/pictures/unidade4_edo997.png}
%     }
%     \caption{Detalhamento da Unidade de Recepção e Processamento (Unidade 4).}
%     \caption*{xxx}
%     \label{fig:unidade4_edo997}
% \end{figure}

% Os transmissores, representados pelas Unidades 6, 7 e 8, desempenham um papel fundamental 
% ao amplificar o sinal a ser transmitido pelo transdutor, o qual é gerado com base no modo 
% de operação selecionado no Console do Operador (Unidade 1).

% No sistema EDO 997, a Unidade 1, denominada Console do Operador, é composta pelos seguintes 
% módulos:

% \begin{itemize}
%     \item Módulo 1A1: Painel frontal do console, contendo controles e indicadores.
%     \item Módulo 1A2: Unidade de display superior, para apresentação gráfica de informações 
%     essenciais.
%     \item Módulo 1A3: Unidade de display inferior, fornecendo informações adicionais 
%     relevantes.
%     \item Módulo 1A4: Painel de bancada, para acesso conveniente a recursos específicos 
%     do console.
%     \item Módulo 1A5: Conjunto de armazenamento e processamento, responsável pela 
%     gestão de dados e processamento do sistema.
% \end{itemize}
    
% A Figura \ref{fig:unidade1_edo997} apresenta a disposição física dos módulos da Unidade 1, 
% permitindo uma visão clara e abrangente do Console do Operador no sistema EDO 997.

% No EDO 997, a Unidade 1 é composta pelos módulos 1A1 (painel frontal), 1A2 (unidade de
% display superior), 1A3 (unidade de display inferior), 1A4 (painel de bancada) e 1A5 
% (conjunto de armazenamento e processamento), mostrados na Figura \ref{fig:unidade1_edo997}.

% \begin{figure}[!h]
%     \centering{
%     \includegraphics[width=.6\linewidth]{/home/<USER>/latex/Sonat_Mk III/resource/pictures/unidade1_edo997.png}
%     }
%     \caption{Detalhamento do Console do Operador (Unidade 1).}
%     \caption*{xxx}
%     \label{fig:unidade1_edo997}
% \end{figure}

% A Unidade 9, conhecida como Condicionador de Sinais, atua como chaveador, alternando o 
% transdutor entre os instantes de transmissão e recepção. Além disso, realiza a 
% pré-amplificação e filtragem analógica dos sinais recebidos pelo transdutor. A interface 
% entre o Condicionador e o Transdutor é assegurada por duas Caixas de Junção 
% (Unidades 13 e 14), que possuem transformadores para o casamento de impedância entre os 
% circuitos dos staves dos transdutores e a saída do Transmissor.

% O Transdutor é um arranjo cilíndrico formado por 360 transdutores individuais distribuídos 
% em 36 staves verticais, cada uma contendo 10 transdutores individuais. O Treinador, representado pela Unidade 17, é um componente essencial que permite ao 
% operador injetar sinais simulados no receptor. Essa funcionalidade é especialmente útil 
% para fins de treinamento e capacitação.


% \subsection{Sistema \ac{sonat}}\label{subsec:sistema_sonat}

% \subsection{Unidade 1 - Revitalizada}


% A Unidade 1 do \ac{sonat} é composta por um computador comercial robustecido, um switch de 16 
% portas, um Filtro de Linha Bivolt, um Nobreak Senoidal 1000-1500VA e dois monitores touch 
% screen 24''. A instalação e disposição dos componentes pode ser vista nas
% Figuras 4, 5 e 6.

% A Unidade 1 do Sistema \ac{sonat} é uma configuração que engloba componentes essenciais para a 
% operação adequada e eficiente do sistema. A seguir, descrevemos os elementos que compõem a Unidade 1:

%     Computador Comercial Robustecido: A Unidade 1 é equipada com um computador comercial de alta resistência e desempenho, especialmente adaptado para atender às necessidades específicas do sistema.

%     Switch de 16 Portas: O switch de 16 portas oferece conectividade eficiente e confiável entre os diversos componentes do sistema, permitindo a comunicação fluida entre eles.

%     Filtro de Linha Bivolt: O Filtro de Linha Bivolt é responsável por garantir a proteção contra oscilações e variações na rede elétrica, preservando a integridade dos componentes eletrônicos do sistema.

%     Nobreak Senoidal 1000-1500VA: O Nobreak Senoidal com capacidade de 1000-1500VA oferece uma fonte de alimentação ininterrupta, garantindo que o sistema mantenha sua operação mesmo em caso de interrupções no fornecimento de energia elétrica.

%     Dois Monitores Touch Screen 24'': A Unidade 1 conta com dois monitores touch screen de 24 polegadas, proporcionando uma interface interativa e amigável para o operador do sistema.

% Instalação e Disposição dos Componentes:
% As Figuras 4, 5 e 6 apresentam a instalação e a disposição dos componentes na Unidade 1 do Sistema \ac{sonat}. Certifique-se de seguir as orientações fornecidas nas respectivas figuras para uma montagem correta e funcional do sistema.

\end{document}